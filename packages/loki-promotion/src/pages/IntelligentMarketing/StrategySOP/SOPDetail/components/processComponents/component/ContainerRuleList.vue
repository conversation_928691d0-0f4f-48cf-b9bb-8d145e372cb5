<template>
  <div class="ContainerRuleList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        @click="jumpToCreate"
      >新建容器规则</Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        :disabled="nextStepLoading"
        @click="handleChangeStep"
      >已有容器规则，下一步</Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="table">
      <Table
        :columns="tableColumns"
        :data-source="list"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
  import {
    Button, Space, Table, Text,
  } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import { defineProps, ref } from 'vue'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const nextStepLoading = ref(false)

  const {
    list, jumpToCreate, extraConfig,
  } = useTableInfo(props, 'containerRuleList')

  const handleChangeStep = async () => {
    nextStepLoading.value = true
    try {
      await props.handleNextStep(props.info)
    } finally {
      nextStepLoading.value = false
    }
  }

  const goDetail = id => {
    window.open(`/loki/promotion/intelligent-marketing/manage-point-container-detail?type=view&id=${id}&isSelect=true`)
  }

  const tableColumns = ref<ThContent[]>([
    {
      title: '容器规则ID',
      dataIndex: 'id',
      minWidth: 60,
    },
    {
      title: '容器规则名称',
      dataIndex: 'name',
      minWidth: 175,
      fixed: 'left',
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      minWidth: 110,
      render: ({ rowData }) => (
      <Space>
        <Text link onClick={() => goDetail(rowData.id)}>查看</Text>
      </Space>
  ),
    },
  ])
</script>

<style scoped lang="stylus">
@import './Index.styl'
.ContainerRuleList {
  /* Styles */
}
</style>
