<template>
  <Modal
    v-model:visible="visible"
    :title="modalTitle"
    :with-footer="true"
    :closeable="true"
    @cancel="handleClose"
  >
    <FormItem
      name="uploadFile"
      label="上传文件"
      on-error="必填项"
      required
    >
      <Upload
        v-model="fileList"
        :custom-request="customRequest"
        :accept="uploadAccept"
        :limit="uploadLimit"
        required
        style="margin-left: 20px"
      >
        <template #tip>
          <Space
            v-if="props.templateLink"
            direction="vertical"
            align="start"
            style="margin-top: 16px"
          >
            <Text
              link
              @click="downloadTemplate"
            >下载模板</Text>
            <Text>支持{{ uploadAccept.split(',').join('、') }}文件</Text>
            <Text v-if="moreTip">{{ moreTip }}</Text>
          </Space>
        </template>
      </Upload>
    </FormItem>
    <template #footer>
      <Space
        justify="end"
        block
      >
        <Button
          :disabled="uploadLoading"
          @click="handleClose"
        >取消</Button>
        <Button
          v-if="fileList && fileList.length > 0"
          type="primary"
          :loading="uploadLoading"
          :disabled="disabled || uploadLoading"
          @click="handleConfirm"
        >开始处理</Button>
      </Space>
    </template>
  </Modal>
</template>

<script setup lang="ts">
  import {
    Button,
    FormItem,
    Modal,
    Space,
    Text, toast,
    Upload,
  } from '@xhs/delight'
  import download from 'loki-shared/utils/download'
  import { createFileUploader } from 'loki-shared/utils/upload'
  import { ref, withDefaults } from 'vue'

  const props = withDefaults(defineProps<{
    modalTitle: string
    uploadLimit: number
    uploadAccept: string
    templateLink?: string
    templateName?: string
    moreTip?: string
  }>(), {
    uploadLimit: 1,
    uploadAccept: '.xls,.xlsx,.cvs,.csv',
    moreTip: '',
    templateLink: '',
    templateName: '',
  })

  const emits = defineEmits(['close', 'download-template', 'upload-template', 'upload-success'])

  const visible = ref(false)
  const fileList = ref()
  const disabled = ref(true)
  const fileUrl = ref()
  const uploadLoading = ref(false)

  const customRequest = ({
    onSuccess, onProgress, file, onError,
  }) => {
    emits('upload-template')
    disabled.value = false
    const ext = (file.name as string).slice((file.name as string).lastIndexOf('.') + 1)
    let fileUploader
    try {
      fileUploader = createFileUploader({ fileFormat: ext })
      fileUploader.post({
        Body: file,
        onProgress({ percent: p }) {
          onProgress({ percent: p * 10 })
        },
      }).then(res => {
        if (res.success) {
          fileUrl.value = res.data.url
          onSuccess({ url: res.data.url })
        } else {
          onError()
        }
      }).catch(err => {
        // eslint-disable-next-line no-console
        console.log(err)
      })
    } catch (err) {
      // eslint-disable-next-line no-console
      console.log(err, file)
      throw new Error(err)
    }
  }

  const downloadTemplate = () => {
    emits('download-template')
    download(props.templateLink, props.templateName)
  }

  const handleClose = () => {
    visible.value = false
    fileList.value = []
    fileUrl.value = ''
    disabled.value = true
    emits('close')
  }

  const handleConfirm = async () => {
    uploadLoading.value = true
    try {
      if (fileUrl.value) {
        emits('upload-success', fileUrl.value)
        handleClose()
      } else {
        toast.warning('请上传文件')
      }
    } finally {
      uploadLoading.value = false
    }
  }

  const showModal = () => {
    visible.value = true
  }

  defineExpose({
    showModal,
  })
</script>
