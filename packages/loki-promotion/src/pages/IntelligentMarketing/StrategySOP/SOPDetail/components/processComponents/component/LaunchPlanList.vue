<template>
  <div class="LaunchPlanList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        @click="jumpToCreate"
      >新建投放计划</Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        :disabled="nextStepLoading"
        @click="handleChangeStep"
      >已有投放计划，下一步</Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="table">
      <Table
        :columns="tableColumns"
        :data-source="list"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
  import {
    Button, Space, Table, Text,
  } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import { defineProps, ref } from 'vue'
  import { formatTime, getStatusName, getTagName } from '~/pages/IntelligentMarketing/LaunchPlan/utils'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const nextStepLoading = ref(false)

  const {
    list, jumpToCreate, extraConfig,
  } = useTableInfo(props, 'launchPlanList')

  const handleChangeStep = async () => {
    nextStepLoading.value = true
    try {
      await props.handleNextStep(props.info)
    } finally {
      nextStepLoading.value = false
    }
  }

  const goDetail = id => {
    window.open(`/loki/promotion/intelligent-marketing/launch-plan-detailV2?type=view&id=${id}`)
  }

  const tableColumns = ref<ThContent[]>([
    {
      title: '投放计划id',
      dataIndex: 'id',
      minWidth: 120,
      fixed: true,
    },
    {
      title: '投放计划名称',
      dataIndex: 'name',
      minWidth: 100,
    },
    {
      title: '标签',
      dataIndex: 'label',
      minWidth: 100,
      render: ({ rowData }) => (
      <Space>
        <Text>{ getTagName(rowData.label) }</Text>
      </Space>
    ),
    },
    {
      title: '投放计划时间',
      dataIndex: 'planTime',
      minWidth: 180,
      render: ({ rowData }) => (
      <Space>
        <Text>{`${formatTime(rowData.startAt)}-${formatTime(rowData.endAt)}`}</Text>
      </Space>
    ),
    },
    {
      title: '策略条数',
      dataIndex: 'strategyNum',
      // dataIndex: 'strategyGroupNum',
      minWidth: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      minWidth: 100,
      render: ({ rowData }) => (
      <Space>
        <Text>{ getStatusName(rowData.status) }</Text>
      </Space>
    ),
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      minWidth: 200,
      render: ({ rowData }) => (
      <Space>
        <Text link onClick={() => goDetail(rowData.id)}>查看</Text>
      </Space>
    ),
    },
  ])
</script>

<style scoped lang="stylus">
@import './Index.styl'
.LaunchPlanList {
  /* Styles */
}
</style>
