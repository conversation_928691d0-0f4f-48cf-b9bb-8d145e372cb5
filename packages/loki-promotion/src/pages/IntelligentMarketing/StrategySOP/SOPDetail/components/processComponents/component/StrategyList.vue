<template>
  <div class="StrategyList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        :loading="createLoading"
        @click="goToCreate"
      >新建策略
      </Button>
      <Button
        type="primary"
        size="large"
        :loading="batchCreateLoading"
        @click="showBatchCreateModal"
      >批量新建策略
      </Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        @click="handleChangeStep"
      >已有策略，下一步
      </Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="table table-container">
      <Table
        :columns="tableColumns"
        :data-source="list"
      />
    </div>
    <!-- 批量新建策略 -->
    <BatchCreateModal
      ref="batchCreateModalRef"
      :launch-plan-id="launchPlanInfo?.launchPlanId"
      :launch-plan-name="launchPlanInfo?.launchPlanName"
      @preview="handleBatchOperationPreview"
      @finish="batchStrategyFinish"
    />
    <Modal
      v-model:visible="visible"
      title="新建策略"
      size="auto"
      @confirm="handleClose"
      @cancel="handleClose"
    >
      <Tactics
        ref="tacticsRef"
        :user-id="userId"
        :user-name="userName"
      />
      <template #footer>
        <Space
          block
          justify="center"
        >
          <Button
            size="small"
            :disabled="previewLoading"
            @click="handleClose"
          >
            关闭
          </Button>
          <Button
            type="primary"
            size="small"
            :loading="previewLoading"
            :disabled="previewLoading"
            @click="gotoPreview"
          >
            策略预览
          </Button>
        </Space>
      </template>
    </Modal>
    <Modal
      v-model:visible="previewVisible"
      title="策略预览"
      confirm-text="创建策略"
      class="preview-modal"
      style="width:100%;height:calc(100vh - 80px);max-height:100vh;top:40px"
      :confirm-loading="createLoading"
      :cancel-button-props="{ disabled: createLoading }"
      @cancel="previewVisible = false"
      @confirm="handleCreate"
    >
      <Strategy :data="previewParams" />
    </Modal>
    <Modal
      v-model:visible="viewVisible"
      title="策略预览"
      class="preview-modal"
      style="width:100%;height:calc(100vh - 80px);max-height:100vh;top:40px"
      :with-footer="false"
    >
      <Strategy :data="previewParams" />
    </Modal>
  </div>
</template>

<script setup lang="tsx">
  import {
    Button, Modal, Space, Table, Tag, Text, toast2 as toast,
  } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import dayjs from 'dayjs'
  import _, { cloneDeep } from 'lodash'
  import {
    computed, defineProps, h, nextTick, onMounted, reactive, ref, toRefs,
  } from 'vue'
  import { useStore } from 'vuex'
  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'
  import { STRATEGY_STATE_COLOR_MAP, STRATEGY_STATE_MAP } from '~/pages/IntelligentMarketing/const'
  import BatchCreateModal from '~/pages/IntelligentMarketing/Group/BatchModal/BatchCreateModal.vue'
  import {
    convertExperimentConfig,
  } from '~/pages/IntelligentMarketing/Group/Nodes/Experiment/TestParamsInputV2/utils'
  import Strategy from '~/pages/IntelligentMarketing/Group/Strategy.vue'
  import Tactics from '~/pages/IntelligentMarketing/Group/Tactics/index.vue'
  import { trackerBatchCreateStrategy } from '~/pages/IntelligentMarketing/tracker'
  import { getFrontState } from '~/pages/IntelligentMarketing/utils'
  import { isExperimentValid } from '~/pages/IntelligentMarketing/utils/experiment'
  import { createStrategy, getSOPLaunchPlan } from '~/services/intelligentMarketing'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const {
    list, handleChangeStep, extraConfig,
  } = useTableInfo(props, 'strategies')

  const tacticsRef = ref(null)

  // 添加loading状态
  const createLoading = ref(false)
  const batchCreateLoading = ref(false)
  const nextStepLoading = ref(false)
  const previewLoading = ref(false)

  const { post } = useBroadcastChannel({
    name: 'SOP-channel',
  })

  const store = useStore()
  const userId = computed(() => store.state?.user?.userInfo?.userId)
  const userName = computed(() => store.state?.user?.userInfo?.userName)
  const visible = ref(false)
  const previewVisible = ref(false)
  const viewVisible = ref(false)
  const launchPlanInfo = ref(null)
  const handleClose = () => {
    visible.value = false
  }
  const state = reactive({
    searchParams: {
      name: undefined,
      expFlag: undefined,
      status: undefined,
      id: undefined,
    },
    previewParams: null,
    copyOriginStrategyName: '',
  })

  const handleField = (form: any, planId: any) => {
    // 把from中的数据全部结构出来
    let {
      name,
      triggerPointId,
      frequency,
      conditionConfig,
      prizeRuleId,
      id,
      status,
      crowd,
      experimentConfigs,
      dynamicConfig,
      triggerPointTimeToUseConfig,
      experimentConfigV2,
    } = form

    if (triggerPointTimeToUseConfig && Object.keys(triggerPointTimeToUseConfig).length > 0) {
      triggerPointTimeToUseConfig.liveUrgeUsePrizeRuleIds = prizeRuleId || []
      triggerPointTimeToUseConfig.enableLiveUrgeUseSwitch = true
      prizeRuleId = null
      if (!triggerPointTimeToUseConfig.liveUrgeUseExpireTime) {
        triggerPointTimeToUseConfig.liveUrgeUseExpireTime = 12
      }
      dynamicConfig = JSON.stringify(Object.assign(JSON.parse(dynamicConfig || '{}'), triggerPointTimeToUseConfig))
    }

    const payload: any = {
      strategy: {
        name,
        triggerPointId,
        conditionConfig,
        userPkgID: crowd.crowdId,
        actionConfig: {
          prizeRuleId: prizeRuleId || null,
          dynamicConfig,
        },
        executableFrequencyConfigs: frequency.executableFrequencyConfigs,
        completeFrequencyConfigs: frequency.completeFrequencyConfigs,
        launchPlanId: planId,
        status: 'ONLINE',
        experimentConfigs,
        experimentConfigV2,
      },
      userId: userId.value,
      userName: userName.value,
    }
    if (id) {
      payload.strategy.id = id
      payload.strategy.status = status
    }

    return payload
  }

  const gotoPreview = async () => {
    previewLoading.value = true
    try {
      // 第一步：表单验证
      try {
        await tacticsRef.value?.formRef.form.validate()
      } catch (error) {
        toast.danger({
          description: '表单校验失败',
          closeable: true,
        })
        return
      }

      const validateForm = tacticsRef.value?.formRef.form.values

      if (validateForm?.experimentConfigV2) {
        validateForm.experimentConfigV2 = convertExperimentConfig(validateForm?.experimentConfigV2, false)

        // 第二步：预检查实验配置
        const shouldContinue = await isExperimentValid({
          experimentConfigsV2: validateForm.experimentConfigV2,
        })

        if (!shouldContinue) {
          return
        }
      }

      // 校验催用触点
      if ((validateForm.triggerPointTimeToUseConfig && Object.keys(validateForm.triggerPointTimeToUseConfig).length > 0) && (!validateForm.prizeRuleId || !validateForm.prizeRuleId.length)) {
        toast.danger('催用触点，最少选择一个权益池')
        return
      }

      if (state.copyOriginStrategyName === validateForm.name) {
        const isContinue = await new Promise(resolve => {
          Modal.warning({
            title: '提示',
            content: h(Text, {}, {
              default: () => '名称与原策略名称相同, 是否确认提交？',
            }),
            onConfirm: close => {
              close?.()
              resolve(true)
            },
            onCancel: close => {
              close?.()
              resolve(false)
            },
          })
        })
        if (!isContinue) {
          return
        }
      }

      const form = handleField(JSON.parse(JSON.stringify(tacticsRef.value?.formRef.form.values)), launchPlanInfo.value.launchPlanId)
      previewVisible.value = true
      state.previewParams = form
    } catch (error) {
      toast.danger({
        description: error.message || error.msg || '操作失败',
        closeable: true,
      })
    } finally {
      previewLoading.value = false
    }
  }

  const batchCreateModalRef = ref()
  const showBatchCreateModal = () => {
    trackerBatchCreateStrategy()
    batchCreateModalRef.value.showModal()
  }

  const goToCreate = () => {
    visible.value = true
  }

  const handleBatchOperationPreview = (strategy: any) => {
    state.previewParams = strategy
    viewVisible.value = true
  }

  // 修改或者复制 view 策略
  const editOrCopyTactics = async (form: any, type: string) => {
    // type.value = type
    const data = JSON.parse(JSON.stringify(form))
    const crowd = {
      crowdId: data.userPkgID || 0,
      crowdName: '不限人群',
    }
    // if(data.userPkgID) {
    //   const res = await searchCrowd({ query: data.userPkgID })
    //   crowd.crowdName = res.crowdConfigs?.[0]?.name || '不限人群'
    // }
    const formFiled = {
      id: data.id,
      name: data.name,
      triggerPointId: data.triggerPointId,
      crowd,
      frequency: {
        executableFrequencyConfigs: data.frequencyConfigs || [],
        completeFrequencyConfigs: data.completeFrequencyConfigs || [],
      },
      conditionConfig: data.conditionConfig || {
        subConditionRelation: 'AND',
        subConditionConfigs: [],
      },
      prizeRuleId: data?.actionConfig?.prizeRuleId || null,
      status: data.status,
      experimentConfigs: data.experimentConfigs || [],
      dynamicConfig: data?.actionConfig?.dynamicConfig,
      triggerPointTimeToUseConfig: {},
      experimentConfigV2: convertExperimentConfig(data.experimentConfigV2),
    }

    // 有临期催用数据转化
    if (data?.actionConfig?.dynamicConfig) {
      const dynamicConfigObj = JSON.parse(data?.actionConfig?.dynamicConfig || '{}')
      if (dynamicConfigObj?.enableLiveUrgeUseSwitch) {
        formFiled.triggerPointTimeToUseConfig.liveUrgeUseExpireTime = dynamicConfigObj.liveUrgeUseExpireTime
        formFiled.prizeRuleId = dynamicConfigObj.liveUrgeUsePrizeRuleIds
      }
    }

    if (type === 'copy') {
      delete formFiled.id
      state.copyOriginStrategyName = formFiled.name
      formFiled.status = 'ONLINE'
    }
    if (type === 'view') {
      viewVisible.value = true
      // state.previewParams = handleField(JSON.parse(JSON.stringify(formFiled)), props.planId)
      state.previewParams = { strategyId: formFiled.id }
    } else {
      visible.value = true
      nextTick(() => {
        tacticsRef.value.formRef.form.setValues(cloneDeep(formFiled))
      // tacticsRef.value.getTriggerPointList(data.triggerPointId)
      // tacticsRef.value.getPrizeRuleList(data?.actionConfig?.prizeRuleId || '')
      })
    }
  }

  const viewStrategy = async (data, type) => {
    editOrCopyTactics(data, type)
  }

  function groupByExperimentConfigs(data) {
    const map = new Map()
    for (const item of data) {
      let found = false
      for (const [key, value] of map.entries()) {
        if (_.isEqual(JSON.parse(key), item.experimentConfigs || null)) {
          value.push(item.name)
          found = true
          break
        }
      }
      if (!found) {
        map.set(JSON.stringify(item.experimentConfigs || null), [item.name])
      }
    }
    // 如果你需要返回一个对象，你可以使用Object.fromEntries函数
    return Object.fromEntries(map)
  }

  const experimentConfigs = experimentConfigs => (experimentConfigs?.length
    ? <Space direction="vertical" align="start" size="2px">
    {
      experimentConfigs.map(item => (
        <Tag size="small" style={{ cursor: 'pointer' }}>
          <Text>{item.flag}</Text>
          <Text>=</Text>
          <Text style={{ fontWeight: 'bold' }}>{item.value}</Text>
        </Tag>
      ))
    }
  </Space> : '')

  const handlePrizeRule = array => {
    const list = groupByExperimentConfigs(array)
    return (
    <Space direction="vertical" align="start">
      {
        Object.keys(list).map(key => {
          const leftRule = JSON.parse(key) || []
          return (
            <Space direction="vertical" style={{
              background: 'rgba(136, 136, 136, 0.08)',
              padding: '5px',
              borderRadius: '5px',
            }}>
              {
                experimentConfigs(leftRule)
              }
              {
                <Space direction="vertical">
                  {
                    list[key].map(item => (
                      <Text style="text-wrap: nowrap">{item}</Text>
                    ))
                  }
                </Space>
              }
            </Space>
          )
        })
      }
    </Space>
  )
  }

  const tableColumns = ref<ThContent[]>([
    {
      title: '策略id',
      dataIndex: 'id',
      minWidth: 100,
    },
    {
      title: '策略名称',
      dataIndex: 'name',
      minWidth: 100,
      fixed: 'left',
    },
    {
      title: '场景',
      dataIndex: 'bizsceneNameList',
      minWidth: 110,
      render: ({ rowData: { triggerPoint } }) => (
      <Space direction="vertical" align="start">
        {
          [...new Set(triggerPoint.bizsceneNameList)]?.map(scene => (
            <Text>{scene}</Text>
          ))
        }
      </Space>

    ),
    },
    {
      title: '触发规则',
      dataIndex: 'sceneID',
      minWidth: 200,
      render: ({ rowData: { triggerPoint } }) => {
        const triggerRuleList = (triggerPoint?.triggerRuleList || []).map(item => ({
          ...item,
          name: item.name,
          experimentConfigs: JSON.parse(JSON.stringify(item.experiments || [])),
        }))
        return (
        <Space direction="vertical" align="start" size="20px">
          {
            triggerRuleList?.length ? handlePrizeRule(triggerRuleList) : ''
          }
        </Space>
      )
      },
    },
    {
      title: '容器',
      dataIndex: 'actionConfig',
      minWidth: 200,
      render: ({ rowData: { triggerPoint } }) => {
        const containers = (triggerPoint?.containerRule?.containerList || []).map(item => ({
          ...item,
          name: item?.containerName,
        }))
        return (
        <Space direction="vertical" align="start" size="20px">
          {
            containers?.length ? handlePrizeRule(containers) : ''
          }
        </Space>
      )
      },
    },
    {
      title: '权益池',
      minWidth: 70,
      dataIndex: 'prizeRuleNameTrue',
      render: ({ rowData: { actionConfig } }) => (
      <Text>{actionConfig.prizeRuleName}</Text>
    ),
    },
    {
      title: '权益组',
      dataIndex: 'prizeRuleName',
      minWidth: 200,
      render: ({ rowData: { actionConfig } }) => (
      <Space direction="vertical" align="start">
        {
          actionConfig?.benefitGroupConfigs?.length && handlePrizeRule(actionConfig?.benefitGroupConfigs)
        }
      </Space>
    ),
    },
    {
      title: '投放计划id',
      dataIndex: 'launchPlanId',
      minWidth: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      minWidth: 60,
      render: ({ rowData }) => (
        // @ts-ignore
        rowData.status === 'PAUSE' ? (
        <Tag color="red" size="small">
          暂停
        </Tag>
          ) : (
        <Tag color={STRATEGY_STATE_COLOR_MAP[getFrontState(rowData)]} size="small">
          {STRATEGY_STATE_MAP[getFrontState(rowData)]}
        </Tag>
      )
      ),
    },
    {
      title: '有效期',
      dataIndex: 'time',
      minWidth: 175,
      render: ({ rowData }) => (
      <Space direction="vertical" align="start">
        <Text>{dayjs(rowData.startAt * 1000).format('YYYY-MM-DD HH:mm:ss')}</Text>
        <Text>{dayjs(rowData.endAt * 1000).format('YYYY-MM-DD HH:mm:ss')}</Text>
      </Space>
    ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      minWidth: 70,
      render: ({ rowData }) => (
      <Space>
        <Text link onClick={() => viewStrategy(rowData, 'view')}>查看</Text>
      </Space>
    ),
    },
  ])

  const getLaunchPlanInfo = async () => {
    const id = props?.info?.id || ''
    try {
      const res = await getSOPLaunchPlan(id)
      launchPlanInfo.value = res || {}
    } catch (e) {
      console.error(e)
    }
  }

  const handleCreate = async () => {
    createLoading.value = true
    try {
      const res = await createStrategy(state.previewParams)
      if (res.success) {
        toast.success('创建成功')
        post({
          instanceNodeId: props.info.id,
          id: [res?.data?.id],
          idType: 'strategyId',
        })
        handleClose()
        previewVisible.value = false
      } else {
        toast.danger(res.msg)
      }
    } catch (err) {
      toast.danger(err.message)
    } finally {
      createLoading.value = false
    }
  }

  const batchStrategyFinish = res => {
    post({
      instanceNodeId: props.info.id,
      id: res?.data?.strategyIds,
      idType: 'strategyId',
    })
  }

  const {
    previewParams,
  } = toRefs(state)

  onMounted(() => {
    getLaunchPlanInfo()
  })
</script>

<style scoped lang="stylus">
@import './Index.styl'
.StrategyList {
  /* Styles */

  .table-container {
    overflow-x: auto;
  }
}

</style>
