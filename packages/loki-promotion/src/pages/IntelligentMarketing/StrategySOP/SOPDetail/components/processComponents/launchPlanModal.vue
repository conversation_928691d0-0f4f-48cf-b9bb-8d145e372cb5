// 投放计划列表弹窗

<template>
  <Modal
    v-model:visible="visible"
    title="投放计划"
    size="1200px"
    :with-footer="false"
  >
    <div class="launch-plan-container">
      <!-- 搜索区域 -->
      <div class="search-area">
        <Input
          v-model="searchKeyword"
          placeholder="请输入计划名称搜索"
          style="width: 240px"
          clearable
          @enter="handleSearch"
        />
        <Button
          type="primary"
          :loading="searchLoading"
          @click="handleSearch"
        >搜索</Button>
      </div>

      <!-- 表格区域 -->
      <Table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="false"
      />

      <!-- 分页区域 -->
      <div class="pagination-area">
        <Pagination
          v-model="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          show-size-changer
          @change="() => handlePageChange(false)"
        />
      </div>
    </div>
  </Modal>
</template>

<script setup lang="tsx">
  import {
    Button, Input,
    Modal,
    Pagination,
    Space,
    Table,
    Tag,
    Text, toast2 as toast,
  } from '@xhs/delight'
  import { reactive, ref } from 'vue'
  import { getLaunchPlanDetail } from '../../../../../../services/intelligentMarketing'

  const visible = ref(false)
  const loading = ref(false)
  const searchKeyword = ref('')
  const dataSource = ref([])
  const searchLoading = ref(false)

  const emit = defineEmits(['handleSelectLaunchPlan'])

  // 工具函数从 LaunchPlan/utils.ts 迁移
  const getTagName = label => {
    const tagMap = {
      1: '常规投放',
      2: '活动投放',
      // 添加其他映射
    }
    return tagMap[label] || '未知'
  }

  const getStatusName = status => {
    const statusMap = {
      1: '进行中',
      2: '待启动',
      3: '已结束',
      4: '已下线',
      // 添加其他映射
    }
    return statusMap[status] || '未知'
  }

  const formatTime = timestamp => {
    if (!timestamp) return ''
    const date = new Date(timestamp * 1000)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).replace(/\//g, '-')
  }

  // 查看详情
  const handleViewDetail = record => {
    emit('handleSelectLaunchPlan', record)
  }

  // 表格列定义
  const columns = [
    {
      title: '投放计划id',
      dataIndex: 'id',
      key: 'id',
      render: ({ rowData }) => (
        <Space>
          <div class="tableText">{rowData.id}</div>
          {rowData.version === 0 && <Tag size="small">旧</Tag>}
        </Space>
      ),
    },
    {
      title: '投放计划名称',
      dataIndex: 'name',
      key: 'name',
      render: ({ rowData }) => (
        <div class="tableText">{rowData.name}</div>
      ),
    },
    {
      title: '标签',
      dataIndex: 'label',
      key: 'label',
      render: ({ rowData }) => (
        <div class="tableText">{getTagName(rowData.label)}</div>
      ),
    },
    {
      title: '投放计划时间',
      dataIndex: 'planTime',
      key: 'planTime',
      render: ({ rowData }) => (
        <div class="tableText">
          {formatTime(rowData.startAt)}-{formatTime(rowData.endAt)}
        </div>
      ),
    },
    {
      title: '策略条数',
      dataIndex: 'strategyNum',
      key: 'strategyNum',
      render: ({ rowData }) => (
        <div class="tableText">{rowData.strategyNum}</div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: ({ rowData }) => (
        <div class="tableText">{getStatusName(rowData.status)}</div>
      ),
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      render: ({ rowData }) => (
        <div class="tableText">{rowData.createdBy}</div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: ({ rowData }) => (
        <Space>
          <Text link onClick={() => handleViewDetail(rowData)}>选择</Text>
        </Space>
      ),
    },
  ]

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  })

  // 获取数据
  const fetchData = async (pageReset = true) => {
    if (pageReset) {
      pagination.current = 1
    }

    loading.value = true
    try {
      const params = {
        nameLike: searchKeyword.value,
        page: pagination.current,
        pageSize: pagination.pageSize,
      }

      const res = await getLaunchPlanDetail(params)

      if (res?.launchPlanList) {
        dataSource.value = res.launchPlanList
        pagination.total = res?.total || 0
      } else {
        toast.warning('获取投放计划列表失败')
      }
    } catch (error) {
      toast.warning('获取数据失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  // 打开弹窗
  const openModal = () => {
    visible.value = true
    fetchData()
  }

  // 关闭弹窗
  const closeModal = () => {
    visible.value = false
  }

  // 处理页码变化
  const handlePageChange = (pageReset = true) => {
    fetchData(pageReset)
  }

  // 处理搜索
  const handleSearch = () => {
    searchLoading.value = true
    fetchData(true).finally(() => {
      searchLoading.value = false
    })
  }

  // 对外暴露方法
  defineExpose({
    openModal,
    closeModal,
  })
</script>

<style scoped>
.launch-plan-container {
  padding: 0 16px;
}

.search-area {
  display: flex;
  margin-bottom: 16px;
  gap: 8px;
}

.pagination-area {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.tableText {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}
</style>
