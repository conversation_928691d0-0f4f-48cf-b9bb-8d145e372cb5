<!-- 大促创建投放计划节点 -->
<template>
  <div class="promotion-launch-plan">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        v-if="props.info.status !== SOPStatus.COMPLETED"
        type="primary"
        size="large"
        :loading="createLoading"
        @click="jumpToCreate"
      >新建投放计划
      </Button>
      <Button
        v-if="props.info.status !== SOPStatus.COMPLETED"
        type="primary"
        size="large"
        :loading="bindLoading"
        @click="openLaunchPlanModal"
      >绑定现有投放计划
      </Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        @click="handleChangeStep"
      >下一步
      </Button>
    </div>
    <Text class="title">本次新建/绑定的投放计划</Text>
    <div
      class="table"
      style="margin-top: 20px"
    >
      <Table
        :columns="launchPlanTableColumns"
        :data-source="launchPlanList"
      >
        <template #label="{ rowData }">
          <div class="tableText">{{ getTagName(rowData.label) }}</div>
        </template>
        <template #planTime="{ rowData }">
          <div class="tableText">{{ formatTime(rowData.startAt) }}-{{
            formatTime(rowData.endAt)
          }}
          </div>
        </template>
        <template #status="{ rowData }">
          <div class="tableText">{{ getStatusName(rowData.status) }}</div>
        </template>
        <template #operation="{ rowData }">
          <Space>
            <Text
              link
              @click="() => { goDetail('view', rowData.id) }"
            >查看
            </Text>
            <Text
              link
              @click="() => { goDetail('edit', rowData.id) }"
            >编辑
            </Text>
          </Space>
        </template>
      </Table>
    </div>
    <Text
      class="title"
      style="margin-top: 20px"
    >本次生成的策略</Text>
    <div
      class="table"
      style="margin-top: 20px"
    >
      <Table
        :columns="strategyTableColumns"
        :data-source="strategyList"
      />
    </div>
    <LaunchPlanModal
      ref="launchPlanModalRef"
      @handleSelectLaunchPlan="handleSelectLaunchPlan"
    />
    <Modal
      v-model:visible="viewVisible"
      title="策略预览"
      class="preview-modal"
      style="width:100%;height:calc(100vh - 80px);max-height:100vh;top:40px"
      :with-footer="false"
    >
      <Strategy :data="previewParams" />
    </Modal>
  </div>
</template>

<script setup lang="tsx">
  import {
    Button,
    Modal,
    Space,
    Table,
    Tag,
    Text,
  } from '@xhs/delight'
  import _ from 'lodash'
  import {
    computed,
    defineProps, ref,
  } from 'vue'
  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'
  import Strategy from '~/pages/IntelligentMarketing/Group/Strategy.vue'
  import { formatTime, getStatusName, getTagName } from '~/pages/IntelligentMarketing/LaunchPlan/utils'
  import {
    STRATEGY_STATE_COLOR_MAP, STRATEGY_STATE_MAP,
  } from '~/pages/IntelligentMarketing/const'
  import { getFrontState } from '~/pages/IntelligentMarketing/utils'
  import { ExperimentNode } from '~/types/intelligentMarketing'
  import { InstanceNode } from '../../../types'
  import LaunchPlanModal from '../launchPlanModal.vue'
  import { useTableInfo } from './hooks/useTableInfo'
  import { SOPStatus } from './types'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const launchPlanModalRef = ref()

  // 添加loading状态
  const createLoading = ref(false)
  const bindLoading = ref(false)
  const nextStepLoading = ref(false)

  const {
    handleChangeStep, jumpToCreate,
  } = useTableInfo(props, 'launchPlanList')

  const launchPlanList = computed(() => JSON.parse(props.info.config || '{}').launchPlanList || [])

  const strategyList = computed(() => JSON.parse(props.info.config || '{}').strategies || [])

  const { post } = useBroadcastChannel({
    name: 'SOP-channel',
  })

  const viewVisible = ref(false)
  const previewParams = ref({})

  const goDetail = (type, id) => {
    const domain = window.location.origin
    window.open(`${domain}/loki/promotion/intelligent-marketing/launch-plan-detailV2?type=${type}&id=${id}&fromType=sop`, '_blank')
  }

  function groupByExperimentConfigs(data) {
    const map = new Map()
    for (const item of data) {
      let found = false
      for (const [key, value] of map.entries()) {
        if (_.isEqual(JSON.parse(key), item.experimentConfigs || null)) {
          value.push(item.name)
          found = true
          break
        }
      }
      if (!found) {
        map.set(JSON.stringify(item.experimentConfigs || null), [item.name])
      }
    }
    // 如果你需要返回一个对象，你可以使用Object.fromEntries函数
    return Object.fromEntries(map)
  }

  // 试验参数处理
  const generateExpression = (config: ExperimentNode): string => {
    const build = (
      node: ExperimentNode,
      isRoot: boolean = true,
      parentConnector?: string,
    ): string => {
      if (node.subExperimentConfigV2List.length === 0) {
        return (!node.value && !node.flag) ? '' : `${node.flag || ''} = ${node.value || ''}`
      }

      const childrenExprs = node.subExperimentConfigV2List.map(child => build(child, false, node.subExperimentRelation))

      const connector = node.subExperimentRelation === 'AND' ? ' 且 ' : ' 或 '

      if (isRoot) {
        const rootConnector = `\n${connector.trim()}\n`
        return childrenExprs.join(rootConnector)
      }

      const expr = childrenExprs.join(connector)
      const needsWrap = childrenExprs.length > 1
        || (parentConnector && parentConnector !== node.subExperimentRelation)

      return needsWrap ? `(${expr})` : expr
    }

    return build(config)
  }

  const experimentConfigs = experimentConfigs => (experimentConfigs?.length
    ? <Space direction="vertical" align="start" size="2px">
    {
      experimentConfigs.map(item => (
        <Tag size="small" style={ { cursor: 'pointer' } }>
          <Text>{ item.flag }</Text>
          <Text>=</Text>
          <Text style={ { fontWeight: 'bold' } }>{ item.value }</Text>
        </Tag>
      ))
    }
  </Space> : '')

  const handlePrizeRule = array => {
    const list = groupByExperimentConfigs(array)
    return (
    <Space direction="vertical" align="start">
      {
        Object.keys(list).map(key => {
          const leftRule = JSON.parse(key) || []
          return (
            <Space direction="vertical" style={ { background: 'rgba(136, 136, 136, 0.08)', padding: '5px', borderRadius: '5px' } }>
              {
                experimentConfigs(leftRule)
              }
              {
                <Space direction="vertical" align="start">
                  {
                    list[key].map(item => (
                      <Text style="text-wrap: nowrap">{ item }</Text>
                    ))
                  }
                </Space>
              }
            </Space>
          )
        })
      }
    </Space>
  )
  }

  // view 策略
  const viewTactics = async (form: any) => {
    viewVisible.value = true
    previewParams.value = { strategyId: form.id }
  }

  const launchPlanTableColumns = ref([
    {
      title: '投放计划id',
      dataIndex: 'id',
      minWidth: 120,
      fixed: true,
    },
    {
      title: '投放计划名称',
      dataIndex: 'name',
      minWidth: 100,
    },
    {
      title: '标签',
      dataIndex: 'label',
      minWidth: 100,
    },
    {
      title: '投放计划时间',
      dataIndex: 'planTime',
      minWidth: 200,
    },
    {
      title: '策略条数',
      dataIndex: 'strategyNum',
      minWidth: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      minWidth: 100,
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      minWidth: 200,
    },
  ])

  const strategyTableColumns = ref([
    {
      title: '策略id',
      dataIndex: 'id',
      minWidth: 60,
    },
    {
      title: '策略名称',
      dataIndex: 'name',
      minWidth: 175,
      fixed: 'left',
    },
    {
      title: '触点名称',
      dataIndex: 'triggerPointId',
      minWidth: 100,
      render: ({ rowData: { triggerPoint } }) => (
      <Text>{ triggerPoint?.name }</Text>
    ),
    },
    {
      title: '场景',
      dataIndex: 'bizsceneNameList',
      minWidth: 110,
      render: ({ rowData: { triggerPoint } }) => (
      <Space direction="vertical" align="start">
        {
          [...new Set(triggerPoint?.bizsceneNameList)]?.map(scene => (
            <Text>{ scene }</Text>
          ))
        }
      </Space>

    ),
    },
    {
      title: '触发规则',
      dataIndex: 'sceneID',
      minWidth: 200,
      render: ({ rowData: { triggerPoint } }) => {
        const triggerRuleList = (triggerPoint?.triggerRuleList || []).map(item => ({
          ...item,
          name: item.name,
          experimentConfigs: JSON.parse(JSON.stringify(item.experiments || [])),
        }))
        return (
        <Space direction="vertical" align="start" size="20px">
          {
            triggerRuleList?.length ? handlePrizeRule(triggerRuleList) : ''
          }
        </Space>
      )
      },
    },
    {
      title: '容器',
      dataIndex: 'actionConfig',
      minWidth: 200,
      render: ({ rowData: { triggerPoint } }) => {
        const containers = (triggerPoint?.containerRule?.containerList || []).map(item => ({
          ...item,
          name: item?.containerName,
        }))
        return (
        <Space direction="vertical" align="start" size="20px">
          {
            containers?.length ? handlePrizeRule(containers) : ''
          }
        </Space>
      )
      },
    },
    {
      title: '实验平台参数',
      dataIndex: 'experimentConfigV2',
      minWidth: 200,
      render: ({ rowData: { experimentConfigV2 } }) => {
        // 生成表达式并处理省略号
        const expression = generateExpression(experimentConfigV2)
        const lines = expression.split('\n')
        const maxLength = 10
        const isOverflow = lines.length > maxLength

        // 截断并添加省略号
        const displayedLines = isOverflow
          ? [...lines.slice(0, maxLength), '...']
          : lines
        return (
        <div style={ {
          lineHeight: '20px', fontSize: '14px', color: 'rgba(0, 0, 0, 0.65)', whiteSpace: 'nowrap', background: 'rgba(136, 136, 136, 0.08)', borderRadius: '5px', padding: '5px',
        } }>
          { displayedLines.map((line, index) => (
            <div key={ index } class="expression-line">
              { line }
            </div>
          )) }
        </div>
      )
      },
    },
    {
      title: '权益池',
      dataIndex: 'prizeRuleNameTrue',
      minWidth: 70,
      render: ({ rowData: { actionConfig } }) => (
      <Text>{ actionConfig.prizeRuleName }</Text>
    ),
    },
    {
      title: '权益组',
      dataIndex: 'prizeRuleName',
      minWidth: 300,
      render: ({ rowData: { actionConfig } }) => (
      <Space direction="vertical" align="start">
        {
          actionConfig?.benefitGroupConfigs?.length && handlePrizeRule(actionConfig?.benefitGroupConfigs)
        }
      </Space>
    ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      minWidth: 60,
      render: ({ rowData }) => (
        // @ts-ignore
        rowData.status === 'PAUSE' ? (
        <Tag color="red" size="small">
          暂停
        </Tag>
          ) : (
        <Tag color={ STRATEGY_STATE_COLOR_MAP[getFrontState(rowData)] } size="small">
          { STRATEGY_STATE_MAP[getFrontState(rowData)] }
        </Tag>
      )
      ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      minWidth: 200,
      fixed: 'right',
      render: ({ rowData }) => (
      <Space>
        <Text disabled={ props.info.status !== SOPStatus.COMPLETED } link onClick={ () => viewTactics(rowData) }>查看</Text>
      </Space>
    ),
    },
  ])

  const openLaunchPlanModal = () => {
    launchPlanModalRef.value.openModal()
  }

  const handleSelectLaunchPlan = launchPlan => {
    post({
      instanceNodeId: props.info.id,
      id: [launchPlan.id],
      idType: 'launchPlanId',
    })
    launchPlanModalRef.value.closeModal()
  }
</script>

<style scoped lang="stylus">
@import './Index.styl'
.promotion-coupon {
  /* Styles */

  .tableText {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px
  }
}
</style>
