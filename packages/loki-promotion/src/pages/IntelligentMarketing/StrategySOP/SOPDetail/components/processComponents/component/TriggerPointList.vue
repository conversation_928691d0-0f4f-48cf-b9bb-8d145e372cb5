<template>
  <div class="TriggerPointList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        @click="jumpToCreate"
      >新建触点</Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        :disabled="nextStepLoading"
        @click="handleChangeStep"
      >已有触点，下一步</Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="table">
      <Table
        :columns="tableColumns"
        :data-source="handleList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
  import {
    Button, Space, Table,
    Tag,
    Text,
  } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import { computed, defineProps, ref } from 'vue'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const nextStepLoading = ref(false)

  const {
    list, jumpToCreate, extraConfig,
  } = useTableInfo(props, 'triggerPointList')

  const handleChangeStep = async () => {
    nextStepLoading.value = true
    try {
      await props.handleNextStep(props.info)
    } finally {
      nextStepLoading.value = false
    }
  }

  const handleList = computed(() => list.value.map(item => ({
    ...item,
    containerNameList: item.containerRule?.containerList?.map(item => item.containerName),
    experimentList: item.experimentConfigs?.map(item => `${item.flag}=${item.value}`),
  })))

  const goDetail = id => {
    window.open(`/loki/promotion/intelligent-marketing/manage-point-detail?type=view&id=${id}`)
  }

  const tableColumns = ref<ThContent[]>([
    {
      title: '触点ID',
      dataIndex: 'id',
      minWidth: 120,
      fixed: true,
    },
    {
      title: '触点名称',
      dataIndex: 'name',
      minWidth: 100,
    },
    {
      title: '场景',
      dataIndex: 'bizsceneNameList',
      minWidth: 200,
      render: ({ rowData: { bizsceneNameList } }) => (
      <Space>
        {bizsceneNameList?.length && bizsceneNameList?.map(item => (
          <Tag
          color="blue"
          size="small"
          >{ item }</Tag>
        ))}
      </Space>
    ),
    },
    {
      title: '容器模板',
      dataIndex: 'containerNameList',
      minWidth: 200,
      render: ({ rowData: { containerNameList } }) => (
      <Space>
        {containerNameList?.length && containerNameList?.map(item => (
          <Tag
          color="blue"
          size="small"
          >{ item }</Tag>
        ))}
      </Space>
    ),
    },
    {
      title: '实验参数',
      dataIndex: 'experimentList',
      minWidth: 200,
      render: ({ rowData: { experimentList } }) => (
      <Space>
        {experimentList?.length && experimentList.map(item => (
          <Tag
          color="blue"
          size="small"
          >{ item }</Tag>
        ))}
      </Space>
    ),
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      minWidth: 110,
      render: ({ rowData }) => (
      <Space>
        <Text link onClick={() => goDetail(rowData.id)}>查看</Text>
      </Space>
    ),
    },
  ])

</script>

<style scoped lang="stylus">
@import './Index.styl'
.TriggerPointList {
  /* Styles */
}
</style>
