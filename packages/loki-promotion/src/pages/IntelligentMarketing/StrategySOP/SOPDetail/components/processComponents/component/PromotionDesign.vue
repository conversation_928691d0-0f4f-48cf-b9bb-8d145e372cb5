<!-- 大促权益设计节点 -->
<template>
  <div class="PrizeList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        v-if="info.status !== SOPStatus.COMPLETED"
        type="primary"
        size="large"
        :loading="uploadLoading"
        :disabled="uploadLoading"
        @click="openUploadModal"
      >上传权益设计
      </Button>
      <Button
        v-if="list.length > 0"
        type="primary"
        size="large"
        :loading="nextStepLoading"
        :disabled="nextStepLoading"
        @click="handleChangeStep"
      >下一步
      </Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="table">
      <Table
        :columns="tableColumns"
        :data-source="list"
      />
    </div>
    <UploadModal
      ref="batchCreateModalRef"
      :modal-title="modalTitle"
      :template-link="createTemplateLink"
      template-name="创建权益设计模版"
      upload-accept=".xlsx"
      @upload-success="handleBatchResult"
    />
  </div>
</template>

<script setup lang="tsx">
  import {
    Button, Table, Text,
  } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import {
    defineProps, reactive, ref, toRefs,
  } from 'vue'
  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'
  import { InstanceNode } from '../../../types'
  import UploadModal from '../../UploadModal.vue'
  import { useTableInfo } from './hooks/useTableInfo'
  import { SOPStatus } from './types'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const state = reactive({
    taskParams: {},
    modalTitle: '上传权益设计模版',
    createTemplateLink: '',
    downloadTemplateLink: '',
  })

  const batchCreateModalRef = ref()

  // 添加loading状态
  const uploadLoading = ref(false)
  const nextStepLoading = ref(false)

  const {
    list, extraConfig,
  } = useTableInfo(props, 'rules')

  const handleChangeStep = async () => {
    nextStepLoading.value = true
    try {
      await props.handleNextStep(props.info)
    } finally {
      nextStepLoading.value = false
    }
  }

  state.createTemplateLink = extraConfig.value.templateLink

  const tableColumns = ref<ThContent[]>([
    {
      title: '实验',
      dataIndex: 'experiment',
      minWidth: 120,
    },
    {
      title: '发券场景',
      dataIndex: 'scene',
      minWidth: 100,
    },
    {
      title: '权益池',
      dataIndex: 'name',
      minWidth: 100,
    },
    {
      title: '发券规则',
      dataIndex: 'couponRule',
      minWidth: 200,
    },
  ])

  const { post } = useBroadcastChannel({
    name: 'SOP-channel',
  })

  const openUploadModal = () => {
    batchCreateModalRef.value.showModal()
  }

  const uploadFileDesign = async fileUrl => {
    uploadLoading.value = true
    try {
      await post({
        instanceNodeId: props.info.id,
        updateContents: JSON.stringify({
          prizeDesignLink: fileUrl,
        }),
      })
    } finally {
      uploadLoading.value = false
    }
  }

  const handleBatchResult = async res => {
    await uploadFileDesign(res)
  }

  const {
    createTemplateLink,
    modalTitle,
  } = toRefs(state)

</script>

<style scoped lang="stylus">
@import './Index.styl'
.PrizeList {
  /* Styles */

  .tableText {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px
  }
}
</style>
