<template>
  <div class="CouponList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        @click="jumpToCreate"
      >新建券</Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        :disabled="nextStepLoading"
        @click="handleChangeStep"
      >已有券，下一步</Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="couponTable">
      <Table
        :columns="tableColumns"
        :data-source="list"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Button, Table, Text } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import { ref } from 'vue'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const nextStepLoading = ref(false)

  const {
    list, jumpToCreate, extraConfig,
  } = useTableInfo(props, 'coupons')

  const handleChangeStep = async () => {
    nextStepLoading.value = true
    try {
      await props.handleNextStep(props.info)
    } finally {
      nextStepLoading.value = false
    }
  }

  const tableColumns = ref<ThContent[]>([
    {
      title: '模版ID',
      dataIndex: 'templateId',
      minWidth: 250,
    },
    {
      title: '名称',
      dataIndex: 'couponcName',
      minWidth: 175,
    },
    {
      title: '折扣描述',
      dataIndex: 'discountDesc',
      minWidth: 100,
    },
    {
      title: '有效时间描述',
      dataIndex: 'validTimeDesc',
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
    },
  ])
</script>

<style scoped lang="stylus">
@import './Index.styl'
.CouponList {
  /* Styles */
}
</style>
