<!-- 大促券展示节点 -->
<template>
  <div class="daily-coupon">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        @click="handleChangeStep"
      >下一步
      </Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="form">
      <DelightForm
        ref="formRef"
        name="layout"
        :config="config"
        :components="{
          DelightForm,
          FormLayout,
          FormItem,
          Input,
          Select,
          DatePicker,
          InputNumber,
          TimeConfigRelatively,
        }"
        :scope="{
          disabled: couponInfoStatus === SOPStatus.COMPLETED,
        }"
      />
      <Button
        :disabled="couponInfoStatus === SOPStatus.COMPLETED"
        type="primary"
        size="large"
        :loading="submitLoading"
        @click="confirm"
      >提交
      </Button>

    </div>
  </div>
</template>

<script setup lang="tsx">
  import { Button, Text } from '@xhs/delight'
  import {
    DatePicker,
    DelightForm,
    Input,
    InputNumber,
    Layout,
    Select,
  } from '@xhs/delight-formily'
  import dayjs from 'dayjs'
  import {
    computed,
    defineProps,
    onMounted,
    ref, watch,
  } from 'vue'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'
  import { SOPStatus } from './types'

  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'
  import { SELLER_COUPON_PROMOTION_CHANNEL_LIST, USER_COUPON_PROMOTION_CHANNEL_LIST } from '~/pages/IntelligentMarketing/const/rule'
  import TimeConfigRelatively from '../TimeConfigRelatively.vue'
  import type { RelativeTimeUnit } from '../constants'
  import { RELATIVE_TIME_UNIT } from '../constants'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
    allStepList: InstanceNode[]
  }>()

  const FormLayout = Layout.FormLayout
  const FormItem = Layout.FormItem

  const couponInfoStatus = computed(() => props.allStepList.find(item => item.templateNode.type === 'PROMOTION_COUPON')?.status)

  const {
    handleChangeStep, extraConfig,
  } = useTableInfo(props, 'prizeRules')
  const formRef = ref()

  // 添加loading状态
  const nextStepLoading = ref(false)
  const submitLoading = ref(false)

  const { post } = useBroadcastChannel({
    name: 'SOP-channel',
  })

  const PROMOTION_CHANNEL_LIST_ENUMS = [
    ...USER_COUPON_PROMOTION_CHANNEL_LIST,
    ...SELLER_COUPON_PROMOTION_CHANNEL_LIST,
  ]

  const config = {
    type: 'void',
    'x-decorator': 'FormLayout',
    'x-disabled': '{{ disabled }}',
    'x-decorator-props': {
      layout: 'horizontal',
    },
    properties: {
      oa: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: 'OA单',
          wrapperWidth: 382,
          required: true,
        },
        'x-component': 'Input',
        'x-component-props': {
          placeholder: '请填写OA单',
        },
        'x-validator': {
          required: true,
          triggerType: 'onBlur',
        },
      },
      activityPrefix: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: '薯券活动名称前缀',
          retuired: true,
        },
        'x-component': 'Input',
        'x-validator': {
          required: true,
          triggerType: 'onBlur',
        },
        'x-component-props': {
          placeholder: '请填写薯券活动名称前缀',
        },
      },
      claimTime: {
        type: 'object',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: '可领取时间',
          required: true,
        },
        'x-component': 'DatePicker',
        'x-component-props': {
          isRange: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          unit: 'second',
          placeholder: { start: '开始时间', end: '结束时间' },
        },
        'x-validator': {
          required: true,
          triggerType: 'onBlur',
        },

      },
      useTimeConfig: {
        type: 'Object',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: '可用时间类型',
        },
        'x-component': 'TimeConfigRelatively',
        'x-component-props': {
          disabled: couponInfoStatus.value === SOPStatus.COMPLETED,
        },
        default: {
          useTimeType: '',
          relativeTimeUnit: RELATIVE_TIME_UNIT.DAY,
          start: 0,
          end: 1,
          date: '',
        },
        'x-validator': {
          required: true,
          triggerType: 'onBlur',
        },
      },
      // 推广渠道
      promotionChannel: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: '推广渠道',
        },
        'x-component': 'Select',
        'x-component-props': {
          options: PROMOTION_CHANNEL_LIST_ENUMS,
          placeholder: '请选择推广渠道',
        },
      },
      stockNum: {
        type: 'number',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: '每张券总库存数',
          required: true,
        },
        'x-component': 'InputNumber',
        'x-component-props': {
          placeholder: '请输入每张券总库存数',
          min: 0,
        },
        'x-validator': {
          required: true,
          triggerType: 'onBlur',
        },
      },
      showName: {
        type: 'string',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: '每张券的优惠券名称',
          required: true,
        },
        'x-component': 'Input',
        'x-component-props': {
          placeholder: '请输入每张券的优惠券名称',
        },
        'x-validator': {
          required: true,
          triggerType: 'onBlur',
        },
      },
      userClaimTotalAmount: {
        type: 'number',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: '用户领券总量限制',
          required: true,
        },
        'x-component': 'InputNumber',
        'x-component-props': {
          placeholder: '请输入用户领券总量限制',
        },
        'x-validator': {
          required: true,
          triggerType: 'onBlur',
        },
      },
      userClaimDayAmount: {
        type: 'number',
        'x-decorator': 'FormItem',
        'x-decorator-props': {
          label: '单用户日领取量限制',
          required: true,
        },
        'x-component': 'InputNumber',
        'x-component-props': {
          placeholder: '请输入单用户日领取量限制',
        },
        'x-validator': {
          required: true,
          triggerType: 'onBlur',
        },
      },
    },
  }

  const handleUseTimeConfig = (type, time) => {
    if (!time) return 0

    // 根据类型将时间转换为秒
    if (type === RELATIVE_TIME_UNIT.DAY) {
      // 1天 = 86400秒
      return time * 24 * 60 * 60
    } if (type === RELATIVE_TIME_UNIT.HOUR) {
      // 1小时 = 3600秒
      return time * 60 * 60
    } if (type === RELATIVE_TIME_UNIT.CLAIM_DAY) {
      // 领取当日有效，不需要时间转换
      return 0
    }

    return 0
  }

  const handleUseTimeConfigForFront = (
    validSecondsAfterClaim: number,
    delayValidSecondsAfterClaim: number,
    relativeTimeUnit: RelativeTimeUnit = RELATIVE_TIME_UNIT.DAY,
  ) => {
    // 24小时的秒数
    const oneDaySeconds = 24 * 60 * 60

    if (relativeTimeUnit === RELATIVE_TIME_UNIT.HOUR) {
      return {
        relativeTimeUnit: RELATIVE_TIME_UNIT.HOUR,
        start: Math.ceil(delayValidSecondsAfterClaim / (60 * 60)),
        end: Math.ceil(validSecondsAfterClaim / (60 * 60)),
      }
    }

    if (relativeTimeUnit === RELATIVE_TIME_UNIT.CLAIM_DAY) {
      return {
        relativeTimeUnit: RELATIVE_TIME_UNIT.CLAIM_DAY,
        start: 0,
        end: 0,
      }
    }

    return {
      relativeTimeUnit: RELATIVE_TIME_UNIT.DAY,
      start: Math.ceil(delayValidSecondsAfterClaim / oneDaySeconds),
      end: Math.ceil(validSecondsAfterClaim / oneDaySeconds),
    }
  }

  const confirm = async () => {
    submitLoading.value = true
    try {
      await formRef.value.form.validate()
      const res = formRef.value.form.values
      const params = {
        ...res,
        claimStartTime: dayjs(res.claimTime.start).unix(),
        claimEndTime: dayjs(res.claimTime.end).unix(),
      }

      if (res.useTimeConfig.useTimeType === 'ABSOLUTE') {
        params.useTimeConfig = {
          useTimeType: 'ABSOLUTE',
          useStartTsSecond: dayjs(res.useTimeConfig.date.start).unix(),
          useEndTsSecond: dayjs(res.useTimeConfig.date.end).unix(),
        }
      }
      if (res.useTimeConfig.useTimeType === 'RELATIVE') {
        params.useTimeConfig = {
          useTimeType: 'RELATIVE',
          relativeTimeUnit: res.useTimeConfig.relativeTimeUnit,
          validSecondsAfterClaim: handleUseTimeConfig(res.useTimeConfig.relativeTimeUnit, res.useTimeConfig.end),
          delayValidSecondsAfterClaim: handleUseTimeConfig(res.useTimeConfig.relativeTimeUnit, res.useTimeConfig.start),
        }
      }
      await post({
        instanceNodeId: props.info.id,
        updateContents: JSON.stringify(params),
      })
    } finally {
      submitLoading.value = false
    }
  }

  const handleFrontParams = data => {
    const params = JSON.parse(data)
    if (params) {
      if (params.claimStartTime && params.claimEndTime) {
        params.claimTime = {
          start: dayjs(params.claimStartTime * 1000).format('YYYY-MM-DD HH:mm:ss'),
          end: dayjs(params.claimEndTime * 1000).format('YYYY-MM-DD HH:mm:ss'),
        }
      }
      delete params.claimStartTime
      delete params.claimEndTime
      if (params.useTimeConfig.useTimeType === 'ABSOLUTE') {
        params.useTimeConfig.date = {
          start: dayjs(params.useTimeConfig.useStartTsSecond * 1000).format('YYYY-MM-DD HH:mm:ss'),
          end: dayjs(params.useTimeConfig.useEndTsSecond * 1000).format('YYYY-MM-DD HH:mm:ss'),
        }
      }
      if (params.useTimeConfig.useTimeType === 'RELATIVE') {
        params.useTimeConfig = {
          ...params.useTimeConfig,
          ...handleUseTimeConfigForFront(params.useTimeConfig.validSecondsAfterClaim, params.useTimeConfig.delayValidSecondsAfterClaim, params.useTimeConfig.relativeTimeUnit),
        }
      }
      formRef.value.form.values = { ...params }
    }
  }

  watch(
    () => props.info.config,
    newVal => {
      if (newVal) {
        handleFrontParams(newVal)
      }
    },
  )
  onMounted(() => {
    if (props.info.config) {
      handleFrontParams(props.info.config)
    }
  })

</script>

<style scoped lang="stylus">
@import './Index.styl'
.daily-coupon {
  /* Styles */

  .tableText {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px
  }
}
</style>
