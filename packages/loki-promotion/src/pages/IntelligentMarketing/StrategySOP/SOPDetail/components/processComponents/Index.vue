<template>
  <div class="process-components-container">
    <div class="process-components-notion">
      <div>
        <div>注：{{ info.templateNode.desc }}</div>
        <a
          :href="info.templateNode.docLink"
          target="_blank"
          style="text-decoration: none; color: #3357d9"
        >查看文档</a>
      </div>
    </div>
  </div>

  <Component
    :is="resolveComponentType"
    v-if="resolveComponentType"
    :info="props.info"
    :handle-next-step="props.handleNextStep"
    class="Container"
    :last-step="props.lastStep"
    :from-type="props.fromType"
    :all-step-list="props.allStepList"
  />
  <Button
    v-if="lastStep === props?.info?.templateNode?.type"
    type="primary"
    size="large"
    style="margin-top: 10px"
    :loading="completeLoading"
    @click="Completed"
  >完成</Button>
</template>

<script setup lang="ts">
  import { Button } from '@xhs/delight'
  import { computed, defineProps, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { updateInstanceDetail } from '~/services/intelligentMarketing'
  import { InstanceNode } from '../../types'
  import processComponents from './component/Index'

  const router = useRouter()

  // 添加loading状态
  const completeLoading = ref(false)

  // 定义 props 类型（确保 info 是对象）
  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
    allStepList: InstanceNode[]
  }>()

  // 动态解析组件类型（自动驼峰转换，无需手动维护映射）
  const resolveComponentType = computed(() => {
    const type = props.info?.templateNode?.type
    if (!type) return null

    // 自动将 "COUPON_LIST" 转换为 "CouponList"
    return processComponents?.[
      type
        .toLowerCase()
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join('')
    ]
  })

  const Completed = async () => {
    completeLoading.value = true
    try {
      await updateInstanceDetail({
        sopInstanceNodeId: props.info.id,
        status: 'FINISH',
      })
      router.push({
        name: 'StrategySOP',
      })
    } finally {
      completeLoading.value = false
    }
  }
</script>

<style scoped lang="stylus">
.process-components-container {
  width 100%;
  flex: 1;
}
.process-components-notion {
  background-color #FEFBB6
  padding 20px
  border-radius 10px
  border 1px solid #e5e5e5
  margin-bottom 20px
  line-height 24px
}
</style>
