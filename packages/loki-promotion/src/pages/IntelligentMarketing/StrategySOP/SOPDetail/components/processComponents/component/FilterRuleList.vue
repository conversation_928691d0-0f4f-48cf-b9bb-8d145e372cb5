<template>
  <div class="FilterRuleList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        @click="openCreateDetail"
      >新建筛选规则</Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        :disabled="nextStepLoading"
        @click="handleChangeStep"
      >下一步</Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="table">
      <Table
        :columns="tableColumns"
        :data-source="handleList"
      />
    </div>
    <!-- 创建、编辑、复制条件规则 -->
    <CreateFilterRuleModal
      v-model:visible="isShowCreateModal"
      action="create"
      :filter-rule-info="filterRuleInfo"
      :reload-list="seedMessage"
    />

    <!-- 条件规则详情 -->
    <FilterRuleDetailModal
      v-model:visible="isShowDetailModal"
      :filter-rule-info="filterRuleInfo"
    />
  </div>
</template>

<script setup lang="tsx">
  import {
    Button, Space, Table, Text,
  } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import { get } from 'lodash'
  import { computed, defineProps, ref } from 'vue'
  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'
  import { ENTITY_TYPE_MAP } from '~/pages/IntelligentMarketing/const'
  import CreateFilterRuleModal from '~/pages/IntelligentMarketing/FilterRuleConfig/components/CreateFilterRuleModal/index.vue'
  import FilterRuleDetailModal from '~/pages/IntelligentMarketing/FilterRuleConfig/components/FilterRuleDetailModal/index.vue'
  import { IFilterRuleItem } from '~/types/intelligentMarketing'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const nextStepLoading = ref(false)

  const {
    list, extraConfig,
  } = useTableInfo(props, 'filterRules')

  const handleChangeStep = async () => {
    nextStepLoading.value = true
    try {
      await props.handleNextStep(props.info)
    } finally {
      nextStepLoading.value = false
    }
  }

  const isShowCreateModal = ref(false)
  const isShowDetailModal = ref(false)
  const filterRuleInfo = ref<IFilterRuleItem | null>(null)

  const { post } = useBroadcastChannel({
    name: 'SOP-channel',
  })

  const handleList = computed(() => list.value.map(item => {
    const itemEntityType = get(item, 'ruleEntityType', '')
    return ({
      ruleId: get(item, 'id', 0),
      ruleName: get(item, 'name', ''),
      entityType: itemEntityType,
      entityTypeName: get(ENTITY_TYPE_MAP, `${itemEntityType}.label`, ''),
      creator: get(item, 'creator.name', '').split('@')[0],
    })
  }))

  // 查看条件规则
  const viewFilterRuleDetail = (rowData: IFilterRuleItem) => {
    filterRuleInfo.value = { ...rowData }
    isShowDetailModal.value = true
  }

  const tableColumns = ref<ThContent[]>([
    {
      title: '规则ID',
      dataIndex: 'ruleId',
      align: 'center',
    },
    {
      title: '规则名称',
      dataIndex: 'ruleName',
      align: 'center',
    },
    {
      title: '作用对象',
      dataIndex: 'entityTypeName',
      align: 'center',
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      render: ({ rowData }) => (
      <Space>
        <Text link onClick={() => viewFilterRuleDetail(rowData as IFilterRuleItem)}>查看</Text>
      </Space>
    ),
    },
  ])
  const openCreateDetail = () => {
    isShowCreateModal.value = true
    filterRuleInfo.value = {}
  }

  const seedMessage = res => {
    post({
      instanceNodeId: props.info.id,
      id: [res?.data?.id],
      idType: 'filterRuleId',
    })
  }
</script>

<style scoped lang="stylus">
@import './Index.styl'
.FilterRuleList {
  /* Styles */
}
</style>
