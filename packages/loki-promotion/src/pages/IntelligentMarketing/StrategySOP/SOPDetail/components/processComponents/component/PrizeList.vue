<template>
  <div class="PrizeList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        @click="jumpToCreate"
      >新建权益池
      </Button>
      <Button
        type="primary"
        size="large"
        @click="openModal"
      >批量新建权益池
      </Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        :disabled="nextStepLoading"
        @click="handleChangeStep"
      >已有权益池，下一步
      </Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="table">
      <Table
        :columns="tableColumns"
        :data-source="list"
      />
    </div>
    <UploadModal
      ref="batchCreateModalRef"
      modal-title="批量创建权益池"
      task-name="create_common_benefit_poll"
      module-name="marketing"
      :template-link="createTemplateLink"
      template-name="批量创建权益池模版"
      @get-result="handleBatchResult"
      @download-template="handleDownloadTemplate"
      @upload-template="handleUploadTemplate"
      @handle-longtask="handleLongTask"
    />
  </div>
</template>

<script setup lang="tsx">
  import { Button, Table, Text } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import {
    defineProps, onMounted, reactive, ref, toRefs,
  } from 'vue'
  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'
  import {
    getBizsceneName,
  } from '~/pages/IntelligentMarketing/EquityPool/utils'
  import {
    trackerBatchCreateEquityPoolDownloadTemplate, trackerBatchCreateEquityPoolStartHandle,
    trackerBatchCreateEquityPoolSuccess, trackerBatchCreateEquityPoolUploadTemplate,
  } from '~/pages/IntelligentMarketing/tracker'
  import UploadModal from '~/pages/Launch/components/UploadModal.vue'
  import { getPrizeRule } from '~/services/usergrowth/edith_get_prize_rule'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const batchCreateModalRef = ref()
  const nextStepLoading = ref(false)

  const {
    list, jumpToCreate, extraConfig,
  } = useTableInfo(props, 'prizeRules')

  const handleChangeStep = async () => {
    nextStepLoading.value = true
    try {
      await props.handleNextStep(props.info)
    } finally {
      nextStepLoading.value = false
    }
  }

  const goDetail = id => {
    window.open(`/loki/promotion/intelligent-marketing/equity-pool-detail?type=view&id=${id}`)
  }
  const state = reactive({
    taskName: 'price_range_4_benefit',
    taskParams: {},
    modalTitle: '新建权益池',
    createTemplateLink: '',
    downloadTemplateLink: '',
  })

  const tableColumns = ref<ThContent[]>([
    {
      title: '权益池id',
      dataIndex: 'id',
      minWidth: 120,
      fixed: true,
    },
    {
      title: '权益池名称',
      dataIndex: 'name',
      minWidth: 100,
    },
    {
      title: '投放场景',
      dataIndex: 'bizscene',
      minWidth: 100,
      render: ({ rowData }) => (
      <Text>{getBizsceneName(rowData.bizscene)}</Text>
    ),
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      minWidth: 200,
    },
    {
      title: '编辑',
      dataIndex: 'operation',
      minWidth: 200,
      render: ({ rowData }) => (
      <Text link onClick={() => {
        goDetail(rowData.id)
      }}>查看</Text>),
    },
  ])

  const { post } = useBroadcastChannel({
    name: 'SOP-channel',
  })

  const openModal = () => {
    batchCreateModalRef.value.showModal()
  }
  const handleBatchResult = res => {
    if (res?.success) {
      const data = JSON.parse(res?.extraJson || '{}')
      post({
        instanceNodeId: props.info.id,
        id: data.benefitPoolIds,
        idType: 'prizeRuleId',
      })
      trackerBatchCreateEquityPoolSuccess()
    }
  }
  const handleDownloadTemplate = () => {
    trackerBatchCreateEquityPoolDownloadTemplate()
  }

  const handleUploadTemplate = () => {
    trackerBatchCreateEquityPoolUploadTemplate()
  }

  const handleLongTask = () => {
    trackerBatchCreateEquityPoolStartHandle()
  }
  const getPrizeList = async () => {
    const res = await getPrizeRule({
      status: 'ONLINE',
      page: 1,
      pageSize: 10,
    })
    if (!state.downloadTemplateLink) {
      state.downloadTemplateLink = res?.benefitPoolDownloadTemplateFileUrl
    }
    if (!state.createTemplateLink) {
      state.createTemplateLink = res?.benefitPoolTemplateFileUrl
    }
  }
  onMounted(() => {
    getPrizeList()
  })

  const {
    createTemplateLink,
  } = toRefs(state)
</script>

<style scoped lang="stylus">
@import './Index.styl'
.PrizeList {
  /* Styles */

  .tableText {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px
  }
}
</style>
