<template>
  <div class="ConditionRuleList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        @click="openCreateModal"
      >新建条件规则</Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        :disabled="nextStepLoading"
        @click="handleChangeStep"
      >下一步</Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="table">
      <Table
        :columns="tableColumns"
        :data-source="handleList"
      />
    </div>
    <!-- 创建、编辑、复制条件规则 -->
    <CreateConditionRuleModal
      v-model:visible="isShowCreateModal"
      action="create"
      :condition-rule-info="conditionRuleInfo"
      :reload-list="seedMessage"
    />

    <!-- 条件规则详情 -->
    <ConditionRuleDetailModal
      v-model:visible="isShowDetailModal"
      :condition-rule-info="conditionRuleInfo"
    />
  </div>
</template>

<script setup lang="tsx">
  import {
    Button, Space, Table, Text,
  } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import { computed, defineProps, ref } from 'vue'
  import { useBroadcastChannel } from '~/hooks/useCrossPageCommunication'
  import ConditionRuleDetailModal from '~/pages/IntelligentMarketing/ConditionRuleConfig/components/ConditionRuleDetailModal/index.vue'
  import CreateConditionRuleModal from '~/pages/IntelligentMarketing/ConditionRuleConfig/components/CreateConditionRuleModal/index.vue'
  import RelStrategyNum from '~/pages/IntelligentMarketing/ConditionRuleConfig/components/RelStrategyNum/index.vue'
  import { handleConditionRuleDetailForFront } from '~/pages/IntelligentMarketing/utils'
  import { IConditionRuleItem } from '~/types/intelligentMarketing'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const isShowCreateModal = ref(false)
  const isShowDetailModal = ref(false)
  const conditionRuleInfo = ref<IConditionRuleItem>()

  const { post } = useBroadcastChannel({
    name: 'SOP-channel',
  })

  const nextStepLoading = ref(false)

  const {
    list, extraConfig,
  } = useTableInfo(props, 'conditionRules')

  const handleChangeStep = async () => {
    nextStepLoading.value = true
    try {
      await props.handleNextStep(props.info)
    } finally {
      nextStepLoading.value = false
    }
  }

  const handleList = computed(() => list.value.map(item => handleConditionRuleDetailForFront(item)))

  // 查看条件规则
  const viewConditionRuleDetail = (rowData: IConditionRuleItem) => {
    conditionRuleInfo.value = { ...rowData }
    isShowDetailModal.value = true
  }
  const tableColumns = ref<ThContent[]>([
    {
      title: '规则ID',
      dataIndex: 'ruleId',
      align: 'center',
    },
    {
      title: '规则名称',
      dataIndex: 'ruleName',
      align: 'center',
    },
    {
      title: '关联的策略数量',
      dataIndex: 'reletedStrategyNum',
      align: 'center',
      render: ({ rowData: { relatedStrategyList, relatedStrategyNum } }) => (
        <Space>
          <RelStrategyNum relatedStrategyList={relatedStrategyList} relatedStrategyNum={relatedStrategyNum} />
        </Space>
    ),
    },
    {
      title: '作用对象',
      dataIndex: 'entityTypeName',
      align: 'center',
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      render: ({ rowData }) => (
      <Space>
        <Text link onClick={() => viewConditionRuleDetail(rowData as IConditionRuleItem)}>查看</Text>
      </Space>
    ),
    },
  ])

  const openCreateModal = () => {
    isShowCreateModal.value = true
    conditionRuleInfo.value = {}
  }

  const seedMessage = res => {
    post({
      instanceNodeId: props.info.id,
      id: [res?.data?.id],
      idType: 'conditionRuleId',
    })
  }
</script>

<style scoped lang="stylus">
@import './Index.styl'
.ConditionRuleList {
  /* Styles */
}
</style>
