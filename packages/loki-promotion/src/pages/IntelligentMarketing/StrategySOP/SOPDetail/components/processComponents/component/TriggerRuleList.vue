<template>
  <div class="TriggerRuleList">
    <div
      v-if="props.fromType === 'edit'"
      class="buttons"
    >
      <Button
        type="primary"
        size="large"
        @click="jumpToCreate"
      >新建触发规则</Button>
      <Button
        type="primary"
        size="large"
        :loading="nextStepLoading"
        :disabled="nextStepLoading"
        @click="handleChangeStep"
      >已有触发规则，下一步</Button>
    </div>
    <Text class="title">{{ extraConfig.title }}</Text>
    <div class="table">
      <Table
        :columns="tableColumns"
        :data-source="list"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
  import {
    Button, Space, Table, Text,
  } from '@xhs/delight'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import { defineProps, ref } from 'vue'
  import { InstanceNode } from '../../../types'
  import { useTableInfo } from './hooks/useTableInfo'

  const props = defineProps<{
    info: InstanceNode
    handleNextStep:(step: InstanceNode) => void
    lastStep: string
    fromType: 'view' | 'edit'
  }>()

  const nextStepLoading = ref(false)

  const {
    list, jumpToCreate, extraConfig,
  } = useTableInfo(props, 'triggerRuleList')

  const handleChangeStep = async () => {
    nextStepLoading.value = true
    try {
      await props.handleNextStep(props.info)
    } finally {
      nextStepLoading.value = false
    }
  }

  const goDetail = id => {
    window.open(`/loki/promotion/intelligent-marketing/manage-point-rule-detail?type=view&id=${id}&isSelect=true`)
  }

  const tableColumns = ref<ThContent[]>([
    {
      title: '触发规则',
      dataIndex: 'id',
      minWidth: 60,
    },
    {
      title: '规则名称',
      dataIndex: 'name',
      minWidth: 175,
      fixed: 'left',
    },
    {
      title: '场景',
      dataIndex: 'bizsceneName',
      minWidth: 200,
    },
    {
      title: '时机',
      dataIndex: 'triggerName',
      minWidth: 200,
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      minWidth: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      minWidth: 110,
      render: ({ rowData }) => (
      <Space>
        <Text link onClick={() => goDetail(rowData.id)}>查看</Text>
      </Space>
    ),
    },
  ])
</script>

<style scoped lang="stylus">
@import './Index.styl'
.TriggerRuleList {
  /* Styles */
}
</style>
