<template>
  <div class="filter-rule-page">
    <Space
      direction="vertical"
      block
      size="large"
      align="unset"
    >
      <OutlineFilter
        v-model="queryParams"
        :config="filterConfig"
        style="overflow: auto"
      />
    </Space>
    <main>
      <Button
        type="primary"
        style="margin-bottom: 12px"
        @click="createStrategySOP"
      >新建流程
      </Button>
      <Table
        :columns="tableColumns"
        :data-source="dataSource"
        :loading="isLoading"
      />
      <Pagination
        v-model="pageInfo.page"
        style="margin-top: 20px;"
        :total="total"
        :page-size="pageInfo.size"
        :page-size-options="[10, 20, 30]"
        @update:page-size="onUpdatePageSize"
        @update:current="onPageChange"
      />
      <Modal
        :visible="visible"
        :loading="createLoading"
        :cancel-button-props="{ disabled: createLoading }"
        @cancel="handleModalCancel"
        @confirm="conformToCreate"
      >
        <Form2
          ref="formRef"
          :model="createForm"
          :rules="rules"
        >
          <FormItem2
            label="流程名称"
            name="sopInstanceName"
          >
            <Input
              v-model="createForm.sopInstanceName"
              :disabled="createLoading"
              placeholder="请输入流程名称"
            />
          </FormItem2>
          <FormItem2
            label="流程类型"
            name="sopTemplateId"
          >
            <Select
              v-model="createForm.sopTemplateId"
              :options="templateOption"
              :disabled="createLoading"
              placeholder="请选择流程类型"
            />
          </FormItem2>
        </Form2>
      </Modal>
    </main>
  </div>
</template>

<script lang="tsx" setup>
  import {
    Button,
    Form2, FormItem2,
    Input,
    Modal,
    Pagination,
    Select,
    Space,
    Table, Text,
    toast,
  } from '@xhs/delight'
  import OutlineFilter from '@xhs/delight-material-ultra-outline-filter'
  import { ThContent } from '@xhs/delight/components/Table/interface'
  import {
    h, onMounted,
    ref,
    watch,
  } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    instanceCreate,
    sopInstanceList,
    sopTemplateList,
    updateSOPInstance,
  } from '~/services/intelligentMarketing'
  import { timestampToString } from '~/utils/time'
  import { useListFilter } from './config'

  const router = useRouter()
  const isLoading = ref(false)
  const createLoading = ref(false)
  const stopLoadingMap = ref(new Map()) // 存储每行的停止loading状态
  const dataSource = ref([]) // 当前展示的数据
  const total = ref(0) // 总条数
  const pageInfo = ref({
    page: 1,
    size: 10,
  })
  const visible = ref(false)
  const createForm = ref<{
    sopInstanceName: string
    sopTemplateId: string
  }>(null)

  // 用于存储所有数据
  const formRef = ref()
  const templateOption = ref([])
  const rules = {
    sopInstanceName: [{ required: true, message: '请输入名称' }],
    sopTemplateId: [{ required: true, message: '请选择类型' }],
  }

  const queryParams = ref({
    nameLike: '',
    creatorId: '',
    status: '',
  })

  // 加载所有数据
  const loadData = async () => {
    isLoading.value = true
    try {
      const res: any = await sopInstanceList({
        ...queryParams.value,
        ...pageInfo.value,
      })
      dataSource.value = res?.instances
      total.value = res?.total || res?.instances?.length
    } catch (err) {
      toast.danger({ description: `数据加载失败${err.message}` })
    } finally {
      isLoading.value = false
    }
  }

  const { filterConfig } = useListFilter(loadData)

  // 用户更新 pageSize 时触发此函数
  const onUpdatePageSize = (newPageSize: number) => {
    pageInfo.value.size = newPageSize || 10
    pageInfo.value.page = 1 // 改变 pageSize 时重置页码
  }

  // 用户翻页时触发此函数
  const onPageChange = (newPageNo: number) => {
    pageInfo.value.page = newPageNo || 1
  }

  const gotoInfo = (rowData: any, type: string) => {
    const { id } = rowData
    router.push({
      name: type === 'edit' ? 'EditStrategySOP' : 'ViewStrategySOP',
      params: {
        id,
      },
      query: {
        fromType: type,
      },
    })
  }

  const stopSOP = rowData => {
    Modal.warning({
      title: '确认终止该SOP吗?',
      centered: true,
      content: h(Text, {}, {
        default: () => `请确认是否要终止该SOP(id:${rowData.id})?`,
      }),
      async onConfirm(close) {
        stopLoadingMap.value.set(rowData.id, true)
        try {
          // 更新状态
          const res = await updateSOPInstance({
            sopInstanceId: rowData.id,
            status: 'STOP',
          })
          if (res.code === 0) {
            toast.success({ description: '更新成功', duration: 1500 })
            loadData()
          }
        } catch (error) {
          toast.danger({ description: '操作失败' })
        } finally {
          stopLoadingMap.value.set(rowData.id, false)
        }
        close?.()
      },
    })
  }

  const createStrategySOP = () => {
    createForm.value = {
      sopInstanceName: '',
      sopTemplateId: '',
    }
    visible.value = true
  }

  const handleModalCancel = () => {
    if (!createLoading.value) {
      visible.value = false
    }
  }

  const conformToCreate = async () => {
    try {
      await formRef.value.validate()
      createLoading.value = true

      const { sopInstanceName, sopTemplateId } = createForm.value
      const res = await instanceCreate({
        sopInstanceName,
        sopTemplateId,
      })

      toast.success({ description: '创建成功', duration: 1500 })
      visible.value = false
      router.push({
        name: 'EditStrategySOP',
        params: {
          id: res.id,
        },
        query: {
          fromType: 'edit',
        },
      })
    } catch (error) {
      toast.danger({ description: '创建失败' })
    } finally {
      createLoading.value = false
    }
  }

  const getTemplateList = async () => {
    try {
      const { templates } = await sopTemplateList()
      templateOption.value = templates.map(v => ({ label: v.name, value: v.id }))
    } catch (error) {
      console.error('Failed to fetch template list:', error)
      templateOption.value = []
    }
  }

  const tableColumns = ref<ThContent[]>([
    {
      title: '名称',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '流程类型',
      dataIndex: 'typeName',
      align: 'center',
      render: ({ rowData }) => (
      <Space>
        <Text>{rowData?.template?.name}</Text>
      </Space>
    ),
    },
    {
      title: '流程状态',
      dataIndex: 'status',
      align: 'center',

    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'createAt',
      align: 'center',
      render: ({ rowData }) => (
      <Space>
        <Text>{timestampToString(rowData.createAt)}</Text>
      </Space>
    ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      render: ({ rowData }) => (
      <Space>
        {
          rowData.status === '已终止'
            ? (<Text link onClick={() => gotoInfo(rowData, 'view')}>查看</Text>)
            : (
              <>
                <Text link onClick={() => gotoInfo(rowData, 'edit')}>编辑</Text>
                <Text
                  link
                  disabled={stopLoadingMap.value.get(rowData.id)}
                  onClick={() => stopSOP(rowData)}
                >
                  {stopLoadingMap.value.get(rowData.id) ? '终止中...' : '终止'}
                </Text>
              </>
            )
        }
      </Space>
    ),
    },
  ])

  // pageInfo 改变时重新请求
  watch(pageInfo, () => {
    loadData()
  }, {
    deep: true,
  })

  // 首次加载数据
  onMounted(() => {
    loadData()
    getTemplateList()
  })
</script>

<style lang="stylus">
.filter-rule-page {
  .search-bar {
    width: 70%;
    display: flex;
    gap: 14px;
  }

  main {
    margin-top: 20px;
  }

  .title {
    font-size 20px
    font-weight 500
    margin 40px 0 10px 0
  }
}
</style>
