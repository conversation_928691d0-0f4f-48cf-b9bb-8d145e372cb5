import { toast } from '@xhs/delight'
import type { EnhancedHttpRequestConfig, SharedHttp } from './types'

export default function useErrorHandler(http: SharedHttp) {
  http.interceptors.response.use(null, (error, _, config: EnhancedHttpRequestConfig) => {
    if (config.needToast !== false) {
      const msg = error.message
      toast.danger(msg === 'Error' || !msg ? '网络异常' : msg)
    }

    if (error.status === 401) {
      setTimeout(() => {
        // eslint-disable-next-line no-underscore-dangle
        const url = new URL(`${window.location.origin}${window.__POWERED_BY_QIANKUN__ ? '/loki/login' : '/login'}`)
        url.searchParams.set('redirect', window.location.href)
        window.location.href = url.href
      }, 300)
      return error
    }

    throw error
  })
}
